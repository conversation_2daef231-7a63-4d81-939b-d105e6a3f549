# 分层文本分类项目快速参考指南

## 🔍 代码定位快速索引

### 核心文件功能映射
| 文件 | 主要功能 | 关键类/函数 |
|------|----------|-------------|
| `train.py` | 主训练脚本 | `main()` |
| `processor.py` | 数据处理 | `WOSProcessor`, `DBPProcessor` |
| `models/hierVerb.py` | 核心模型 | `HierVerbPromptForClassification` |
| `models/loss.py` | 损失函数 | `hierarchical_contrastive_loss_func` |
| `util/utils.py` | 工具函数 | `load_plm_from_config`, `_mask_tokens` |
| `util/eval.py` | 评估函数 | `compute_score` |

### 关键代码片段快速定位

#### 1. 数据处理器初始化
```python
# 位置: train.py 第206行
processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)
```

#### 2. 模板创建
```python
# 位置: train.py 第254-257行
text_mask = []
for i in range(args.depth):
    text_mask.append(f'{i + 1} level: {{"mask"}}')
text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
```

#### 3. 多层动词化器创建
```python
# 位置: train.py 第324-328行
for i in range(args.depth):
    verbalizer_list.append(SoftVerbalizer(tokenizer, model=plm, classes=label_list[i]))
```

#### 4. 模型前向传播
```python
# 位置: models/hierVerb.py 第62行
def forward(self, batch) -> torch.Tensor:
```

#### 5. 层级标签构建
```python
# 位置: models/hierVerb.py 第112-126行
hier_labels = []
hier_labels.insert(0, labels)
for idx in range(args.depth - 2, -1, -1):
    # 构建层级标签
```

#### 6. 改进对比学习
```python
# 位置: models/hierVerb.py 第166-178行
contrastive_loss = hierarchical_contrastive_loss_func(
    hier_labels=hier_labels,
    processor=self.processor,
    output_at_mask=outputs_at_mask,
    # ... 更多参数
)
```

## 🎯 常用参数速查

### 训练参数
```bash
# 基础训练参数
--dataset wos                    # 数据集选择
--batch_size 8                   # 批次大小
--lr 3e-5                       # PLM学习率
--lr2 1e-4                      # Verbalizer学习率
--max_epochs 10                 # 最大训练轮次
--shot 30                       # Few-shot样本数
--seed 171                      # 随机种子

# 损失函数参数
--lm_training 1                 # 启用MLM损失
--lm_alpha 0.7                  # MLM损失权重
--constraint_loss 1             # 启用约束损失
--constraint_alpha 1.0          # 约束损失权重
--contrastive_loss 1            # 启用对比学习
--contrastive_alpha 0.8         # 对比学习权重

# 改进对比学习参数
--use_hierarchical_contrastive 1    # 启用改进对比学习
--contrastive_temperature 0.07      # 温度参数
--hierarchical_weight_strategy linear # 权重策略
--contrastive_similarity_threshold 0.5 # 相似度阈值
```

### 设备和优化参数
```bash
--device 0                      # GPU设备号
--use_multi_gpu False           # 是否使用多GPU
--max_grad_norm 1.0            # 梯度裁剪
--early_stop 5                 # 早停轮次
--use_scheduler1 1             # PLM学习率调度
--use_scheduler2 1             # Verbalizer学习率调度
```

## 🔧 常见问题解决方案

### 1. CUDA内存不足
```bash
# 解决方案1: 减小批次大小
--batch_size 4

# 解决方案2: 启用梯度累积
--gradient_accumulation_steps 2

# 解决方案3: 使用CPU训练
--device -1
```

### 2. 收敛缓慢
```bash
# 解决方案1: 调整学习率
--lr 5e-5 --lr2 2e-4

# 解决方案2: 调整温度参数
--contrastive_temperature 0.1

# 解决方案3: 增加对比学习权重
--contrastive_alpha 1.0
```

### 3. 过拟合问题
```bash
# 解决方案1: 启用早停
--early_stop 3

# 解决方案2: 减小学习率
--lr 1e-5

# 解决方案3: 增加MLM损失权重
--lm_alpha 0.8
```

## 📊 性能调优指南

### 1. 参数调优顺序
1. **基础参数**: lr, batch_size, max_epochs
2. **损失权重**: lm_alpha, constraint_alpha, contrastive_alpha
3. **对比学习**: contrastive_temperature, similarity_threshold
4. **高级参数**: weight_strategy, gradient_accumulation_steps

### 2. 监控指标
- **训练损失**: 应该稳定下降
- **验证F1**: 应该稳定提升
- **各项损失比例**: 保持合理平衡

### 3. 实验设计
```bash
# 基线实验
python train.py --contrastive_loss 0

# 原始对比学习
python train.py --contrastive_loss 1 --use_hierarchical_contrastive 0

# 改进对比学习
python train.py --contrastive_loss 1 --use_hierarchical_contrastive 1
```

## 🚀 快速启动命令

### 标准训练
```bash
python train.py \
    --dataset wos \
    --batch_size 8 \
    --max_epochs 10 \
    --shot 30 \
    --seed 171 \
    --contrastive_loss 1 \
    --use_hierarchical_contrastive 1
```

### 消融实验
```bash
# 运行预定义的消融实验
bash run_ablation_study.sh
```

### 完整流程
```bash
# 训练 + 嵌入生成 + Top-K评估
bash run_improved_contrastive.sh
```

## 📁 输出文件说明

### 模型检查点
```
ckpts/
├── {timestamp}-macro.ckpt      # Macro F1最佳模型
├── {timestamp}-micro.ckpt      # Micro F1最佳模型
└── {timestamp}-P_acc.ckpt      # 准确率最佳模型
```

### 结果文件
```
result/
├── few_shot_train.txt          # 训练结果
├── ablation_*.txt              # 消融实验结果
└── improved_contrastive_*.txt  # 改进对比学习结果
```

### TensorBoard日志
```
runs/
├── train/{timestamp}/          # 训练日志
└── integrated_train/{id}/      # 集成训练日志
```

## 🔍 调试技巧

### 1. 启用详细日志
```bash
--print_contrastive_details 1
```

### 2. 监控TensorBoard
```bash
tensorboard --logdir runs/
```

### 3. 检查数据加载
```python
# 在train.py中添加
print(f"Train data length: {len(train_data)}")
print(f"Sample: {train_data[0]}")
```

### 4. 验证层级映射
```python
# 在processor.py中添加
print(f"Hier mapping keys: {list(self.hier_mapping[0][0].keys())}")
```

---

**使用提示**: 这个快速参考指南帮助您快速定位代码和解决常见问题。结合详细的代码解读文档，您可以高效地理解和修改项目。

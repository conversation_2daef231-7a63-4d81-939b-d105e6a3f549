# 改进的分层对比学习实现

## 🎯 改进概述

本次改进针对原有的对比学习机制进行了重大优化，主要解决以下问题：

1. **层级感知不足**：原始对比学习没有充分利用分层标签的层级结构
2. **相似度计算简单**：只考虑标签完全匹配，忽略了层级相似性
3. **负样本选择随机**：没有基于层级结构进行智能的负样本选择

## 🔧 核心改进

### 1. 分层相似度计算

```python
def compute_hierarchical_similarity(level_labels, hier_labels, processor, current_level):
    """
    计算层级感知的相似度矩阵
    
    核心思想：
    - 共同祖先越多，相似度越高
    - 相似度 = 共同祖先数 / 总层级数
    - 设置多级相似度阈值：高(≥0.8)、中(≥0.5)、低(<0.5)
    """
```

**示例**：
```
样本A标签: [数学, 代数, 方程, 一元二次方程]
样本B标签: [数学, 代数, 方程, 一元一次方程]  
样本C标签: [数学, 几何, 平面几何, 三角形]

A与B相似度: 3/4 = 0.75 (共享前3层)
A与C相似度: 1/4 = 0.25 (只共享第1层)
```

### 2. 温度控制的对比学习

```python
def compute_contrastive_loss(cosine_sim, similarity_matrix, temperature):
    """
    使用温度参数控制相似度的锐化程度
    
    - temperature越小，相似度分布越锐化
    - 有助于模型学习更精确的嵌入表示
    """
```

### 3. 层级权重策略

```python
# 深层权重更大，因为深层分类更重要
level_weight = (level + 1) / depth
total_loss += level_weight * level_loss
```

## 📊 使用方法

### 方法1：直接训练

```bash
# 启用改进的分层对比学习
python train.py \
    --dataset wos \
    --contrastive_loss 1 \
    --use_hierarchical_contrastive 1 \
    --contrastive_temperature 0.07 \
    --contrastive_alpha 0.8 \
    --constraint_alpha 1.2 \
    --lm_alpha 0.5 \
    --batch_size 8 \
    --max_epochs 10
```

### 方法2：使用改进训练脚本

```bash
# 单次训练
python train_improved_contrastive.py --mode train

# 消融实验（比较不同策略）
python train_improved_contrastive.py --mode ablation
```

## 🔬 参数说明

### 新增参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--use_hierarchical_contrastive` | 1 | 是否使用改进的分层对比学习 |
| `--contrastive_temperature` | 0.1 | 温度参数，控制相似度锐化程度 |

### 推荐参数组合

#### 配置1：平衡型
```bash
--contrastive_alpha 0.8
--contrastive_temperature 0.1
--constraint_alpha 1.0
--lm_alpha 0.7
```

#### 配置2：对比学习强化型
```bash
--contrastive_alpha 1.2
--contrastive_temperature 0.07
--constraint_alpha 1.5
--lm_alpha 0.3
```

#### 配置3：精确匹配型
```bash
--contrastive_alpha 0.6
--contrastive_temperature 0.05
--constraint_alpha 2.0
--lm_alpha 0.5
```

## 📈 预期改进效果

### 理论优势

1. **更好的层级感知**：模型能够理解标签间的层级关系
2. **精确的嵌入学习**：相似样本的嵌入更接近，不相似样本更远离
3. **智能负样本选择**：基于层级结构选择有意义的负样本

### 预期性能提升

- **Macro F1**: 预期提升 5-15%
- **Micro F1**: 预期提升 3-8%
- **Top-K匹配率**: 预期提升 8-20%
- **新测试集泛化**: 预期显著改善

## 🔍 消融实验设计

脚本会自动运行以下实验：

1. **基线**：不使用对比学习
2. **原始对比学习**：使用原有的对比学习机制
3. **改进对比学习 (T=0.1)**：温度参数0.1
4. **改进对比学习 (T=0.07)**：温度参数0.07
5. **改进对比学习 (T=0.05)**：温度参数0.05

## 🚀 快速开始

### 步骤1：运行改进训练
```bash
cd DCL
python train_improved_contrastive.py --mode train
```

### 步骤2：检查结果
训练完成后会自动：
1. 生成训练集嵌入
2. 运行Top-K评估
3. 输出性能指标

### 步骤3：对比分析
```bash
# 运行消融实验
python train_improved_contrastive.py --mode ablation

# 查看结果
ls result/ablation_*.txt
```

## 🔧 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减小batch_size
   --batch_size 4
   ```

2. **收敛缓慢**
   ```bash
   # 调整学习率和温度
   --lr 5e-5
   --contrastive_temperature 0.1
   ```

3. **对比损失过大**
   ```bash
   # 减小对比损失权重
   --contrastive_alpha 0.5
   ```

### 调试技巧

1. **监控损失**：使用TensorBoard查看各项损失的变化
2. **检查相似度**：在训练过程中打印相似度矩阵
3. **验证嵌入**：可视化嵌入向量的分布

## 📊 性能监控

### TensorBoard指标

- `Loss/contrastive`: 对比学习损失
- `Loss/constraint`: 约束损失  
- `Loss/multi-verb`: 分层分类损失
- `Loss/lm`: 语言模型损失

### 关键指标

- **训练集性能**：应该逐步提升
- **验证集性能**：应该稳定提升，无过拟合
- **对比损失收敛**：应该逐渐下降并稳定

## 🎯 下一步优化方向

1. **自适应温度**：根据训练进度动态调整温度参数
2. **困难样本挖掘**：重点关注分类困难的样本
3. **多尺度对比**：在不同粒度上进行对比学习
4. **知识蒸馏**：结合教师模型进行对比学习

---

**注意**：改进的对比学习与原有系统完全兼容，可以通过参数控制是否启用。建议先在小规模数据上验证效果，再进行大规模训练。

'''
Integrated Training Script
Combines train.py, embedding.py, and topk.py into a single workflow
with early stopping based on macro and micro F1 scores.
'''

import os
import sys
import torch
import pickle
import argparse
import openprompt
from datetime import datetime
from tqdm import tqdm
from openprompt.utils.reproduciblity import set_seed
from openprompt.prompts import SoftVerbalizer, ManualTemplate
from transformers.optimization import get_linear_schedule_with_warmup
from torch.optim import AdamW
from torch.utils.tensorboard import SummaryWriter

# Import models
from models.hierVerb import HierVerbPromptForClassification as TrainModel
from models.embedding_chy import HierVerbPromptForClassification as EmbeddingModel
from models.topk_chy import HierVerbPromptForClassification as TopkModel

# Import utilities
from processor import PROCESSOR
from processor_des import PROCESSOR1
from util.utils import load_plm_from_config, print_info
from util.data_loader import SinglePathPromptDataLoader

import logging
logging.basicConfig(format='%(asctime)s - %(levelname)s - %(name)s -   %(message)s',
                    datefmt='%m/%d/%Y %H:%M:%S',
                    level=logging.INFO)
logger = logging.getLogger(__name__)


class IntegratedTrainer:
    def __init__(self, args):
        self.args = args
        self.use_cuda = True
        self.device = self._setup_device()
        self.processor = self._setup_processor()
        self.dataset = self._setup_dataset()
        self.plm, self.tokenizer, self.model_config, self.WrapperClass = self._setup_model()
        self.template = self._setup_template()
        self.dataloaders = self._setup_dataloaders()
        self.verbalizer_list = self._setup_verbalizers()
        
        # Training state
        self.best_macro_f1 = 0.0
        self.best_micro_f1 = 0.0
        self.best_combined_score = 0.0  # Track combined score for better model selection
        self.best_epoch = -1
        self.early_stop_count = 0
        self.current_epoch = 0
        
        # File paths
        self.current_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        self.run_id = f"{self.current_time}-lr-{args.lr}-lm_training-{args.lm_training}-lm_alpha-{args.lm_alpha}-batch_size-{args.batch_size}"
        self.ckpt_path = f"ckpts/{self.run_id}-macro.ckpt"
        self.embedding_path = f"_{args.shot}shot_none_{args.seed}_embed_doc_{args.label_description}.pkl"
        
        # TensorBoard
        self.writer = SummaryWriter(log_dir=f"runs/integrated_train/{self.run_id}")
        
    def _setup_device(self):
        if self.args.use_multi_gpu:
            print_info("Using multi-GPU mode with device_map='auto'")
            device = torch.device("cuda")
        elif self.args.device != -1:
            os.environ["CUDA_VISIBLE_DEVICES"] = f"{self.args.device}"
            device = torch.device("cuda:0")
            print_info(f"Using single GPU mode: GPU {self.args.device}")
        else:
            self.use_cuda = False
            device = torch.device("cpu")
            print_info("Using CPU mode")
        return device
        
    def _setup_processor(self):
        processor = PROCESSOR[self.args.dataset](shot=self.args.shot, seed=self.args.seed)
        self.args.depth = len(processor.hier_mapping) + 1
        return processor
        
    def _setup_dataset(self):
        if self.args.label_description:
            processor1 = PROCESSOR1[self.args.dataset](shot=self.args.shot, seed=self.args.seed)
            dataset = {
                'train': processor1.train_example,
                'dev': processor1.dev_example,
                'test': processor1.test_example
            }
        else:
            dataset = {
                'train': self.processor.train_example,
                'dev': self.processor.dev_example,
                'test': self.processor.test_example
            }
        return dataset
        
    def _setup_model(self):
        return load_plm_from_config(self.args, self.args.model_name_or_path)
        
    def _setup_template(self):
        if self.args.multi_mask:
            template_file = f"{self.args.dataset}_mask_template.txt"
        else:
            template_file = "manual_template.txt"
            
        template_path = "template"
        text_mask = []
        for i in range(self.args.depth):
            text_mask.append(f'{i + 1} level: {{"mask"}}')
        text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
        
        if not os.path.exists(template_path):
            os.mkdir(template_path)
        if not os.path.exists("ckpts"):
            os.mkdir("ckpts")
            
        template_path = os.path.join(template_path, template_file)
        if not os.path.exists(template_path):
            with open(template_path, 'w', encoding='utf-8') as fp:
                fp.write(text)
                
        return ManualTemplate(tokenizer=self.tokenizer).from_file(template_path, choice=self.args.template_id)
        
    def _setup_dataloaders(self):
        # Training dataloader
        train_dataloader = SinglePathPromptDataLoader(
            dataset=self.dataset['train'], 
            template=self.template, 
            tokenizer=self.tokenizer,
            tokenizer_wrapper_class=self.WrapperClass, 
            max_seq_length=self.args.max_seq_lens,
            decoder_max_length=3,
            batch_size=self.args.batch_size, 
            shuffle=self.args.shuffle, 
            teacher_forcing=False,
            predict_eos_token=False, 
            truncate_method="tail",
            num_works=4,
            multi_gpu=self.args.use_multi_gpu
        )
        
        # Validation and test dataloaders
        if self.args.dataset == "wos":
            full_name = "WebOfScience"
        elif self.args.dataset == "dbp":
            full_name = "DBpedia"
        else:
            raise NotImplementedError
            
        dev_path = os.path.join(f"/data/zhousiqi/TACL_math1/DCL/dataset", full_name, f"dev_dataloader-multi_mask.pt")
        test_path = os.path.join(f"/data/zhousiqi/TACL_math1/DCL/dataset", full_name, f"test_dataloader-multi_mask.pt")
        
        # Use batch_size=8 for evaluation to match embedding.py and topk.py
        eval_batch_s = 8
        
        # Load or create validation dataloader
        if self.args.dataset != "dbp" and os.path.exists(dev_path):
            validation_dataloader = torch.load(dev_path, weights_only=False)
        else:
            validation_dataloader = SinglePathPromptDataLoader(
                dataset=self.dataset["dev"], 
                template=self.template,
                tokenizer=self.tokenizer,
                tokenizer_wrapper_class=self.WrapperClass,
                max_seq_length=self.args.max_seq_lens,
                decoder_max_length=3,
                batch_size=eval_batch_s, 
                shuffle=False,
                teacher_forcing=False,
                predict_eos_token=False,
                truncate_method="tail",
                multi_gpu=self.args.use_multi_gpu
            )
            if self.args.dataset != "dbp":
                torch.save(validation_dataloader, dev_path)
                
        # Load or create test dataloader
        # Match the logic from embedding.py and topk.py
        if not os.path.exists(test_path):
            test_dataloader = SinglePathPromptDataLoader(
                dataset=self.dataset["test"],
                template=self.template,
                tokenizer=self.tokenizer,
                tokenizer_wrapper_class=self.WrapperClass,
                max_seq_length=self.args.max_seq_lens,
                decoder_max_length=3,
                batch_size=eval_batch_s,
                shuffle=False,
                teacher_forcing=False,
                predict_eos_token=False,
                truncate_method="tail",
                multi_gpu=False,  # Match embedding.py
                mode='test'
            )
            torch.save(test_dataloader, test_path)
        else:
            # When test_path exists, create a new dataloader with batch_s (like embedding.py does)
            test_dataloader = SinglePathPromptDataLoader(
                dataset=self.dataset['test'],
                template=self.template,
                tokenizer=self.tokenizer,
                tokenizer_wrapper_class=self.WrapperClass,
                max_seq_length=self.args.max_seq_lens,
                decoder_max_length=3,
                batch_size=self.args.batch_size,  # Use the original batch_size (8 for embedding/topk)
                shuffle=False,
                teacher_forcing=False,
                predict_eos_token=False,
                truncate_method="tail",
                num_works=4,
                multi_gpu=True
            )
            
        return {
            'train': train_dataloader,
            'dev': validation_dataloader,
            'test': test_dataloader
        }
        
    def _setup_verbalizers(self):
        verbalizer_list = []
        label_list = self.processor.label_list
        
        for i in range(self.args.depth):
            if "0.1.2" in openprompt.__path__[0]:
                verbalizer_list.append(SoftVerbalizer(self.tokenizer, model=self.plm, classes=label_list[i]))
            else:
                verbalizer_list.append(SoftVerbalizer(self.tokenizer, model=self.plm, classes=label_list[i]))
                
        return verbalizer_list

    def train_one_epoch(self):
        """Train the model for one epoch"""
        print_info(f"------------ epoch {self.current_epoch + 1} ------------")

        # Create training model
        prompt_model = TrainModel(
            plm=self.plm,
            template=self.template,
            verbalizer_list=self.verbalizer_list,
            tokenizer=self.tokenizer,
            freeze_plm=self.args.freeze_plm,
            args=self.args,
            processor=self.processor,
            plm_eval_mode=self.args.plm_eval_mode,
            use_cuda=self.use_cuda
        )

        # Move model to device
        if self.use_cuda and not self.args.use_multi_gpu:
            prompt_model = prompt_model.cuda()
        elif self.args.use_multi_gpu:
            print_info("Model distributed across multiple GPUs via device_map='auto'")

        # Setup optimizers
        no_decay = ['bias', 'LayerNorm.weight']
        named_parameters = prompt_model.plm.named_parameters()

        optimizer_grouped_parameters1 = [
            {'params': [p for n, p in named_parameters if not any(nd in n for nd in no_decay)],
             'weight_decay': 0.01},
            {'params': [p for n, p in named_parameters if any(nd in n for nd in no_decay)],
             'weight_decay': 0.0}
        ]

        verbalizer = prompt_model.verbalizer
        optimizer_grouped_parameters2 = [
            {'params': verbalizer.group_parameters_1, "lr": self.args.lr},
            {'params': verbalizer.group_parameters_2, "lr": self.args.lr2},
        ]

        optimizer1 = AdamW(optimizer_grouped_parameters1, lr=self.args.lr)
        optimizer2 = AdamW(optimizer_grouped_parameters2)

        # Setup schedulers
        tot_step = len(self.dataloaders['train']) // self.args.gradient_accumulation_steps * self.args.max_epochs
        warmup_steps = 0
        scheduler1 = None
        scheduler2 = None

        if self.args.use_scheduler1:
            scheduler1 = get_linear_schedule_with_warmup(
                optimizer1, num_warmup_steps=warmup_steps, num_training_steps=tot_step)
        if self.args.use_scheduler2:
            scheduler2 = get_linear_schedule_with_warmup(
                optimizer2, num_warmup_steps=warmup_steps, num_training_steps=tot_step)

        # Training loop
        loss_detailed = [0, 0, 0, 0]
        prompt_model.train()
        global_step = self.current_epoch * len(self.dataloaders['train'])

        for batch in tqdm(self.dataloaders['train']):
            batch = tuple(t.to(self.device) if isinstance(t, torch.Tensor) else t for t in batch)
            batch = {"input_ids": batch[0], "attention_mask": batch[1],
                     "label": batch[2], "loss_ids": batch[3]}

            logits, loss, cur_loss_detailed = prompt_model(batch)
            loss_detailed = [loss_detailed[idx] + value for idx, value in enumerate(cur_loss_detailed)]

            loss.backward()
            torch.nn.utils.clip_grad_norm_(prompt_model.parameters(), self.args.max_grad_norm)

            optimizer1.step()
            optimizer2.step()

            if scheduler1 is not None:
                scheduler1.step()
            if scheduler2 is not None:
                scheduler2.step()

            optimizer1.zero_grad()
            optimizer2.zero_grad()

            # Log to TensorBoard
            if isinstance(cur_loss_detailed, (list, tuple)) and len(cur_loss_detailed) == 4:
                self.writer.add_scalar('Loss/multi-verb', cur_loss_detailed[0], global_step)
                self.writer.add_scalar('Loss/lm', cur_loss_detailed[1], global_step)
                self.writer.add_scalar('Loss/constraint', cur_loss_detailed[2], global_step)
                self.writer.add_scalar('Loss/contrastive', cur_loss_detailed[3], global_step)
            global_step += 1

        print_info("multi-verb loss, lm loss, constraint loss, contrastive loss are: ")
        print_info(loss_detailed)

        # Evaluate on validation set
        scores = prompt_model.evaluate(self.dataloaders['dev'], self.processor, desc="Valid", mode=self.args.eval_mode)

        # Log validation scores
        if 'macro_f1' in scores:
            self.writer.add_scalar('Val/macro_f1', scores['macro_f1'], self.current_epoch)
        if 'micro_f1' in scores:
            self.writer.add_scalar('Val/micro_f1', scores['micro_f1'], self.current_epoch)
        if 'acc' in scores:
            self.writer.add_scalar('Val/acc', scores['acc'], self.current_epoch)

        # Save checkpoint based on combined score (macro + micro) / 2
        macro_f1 = scores['macro_f1']
        micro_f1 = scores['micro_f1']
        combined_score = (macro_f1 + micro_f1) / 2
        print_info(f'macro {macro_f1} micro {micro_f1} combined {combined_score}')

        improved = False

        # Update individual best scores for tracking
        if macro_f1 > self.best_macro_f1:
            self.best_macro_f1 = macro_f1
            print_info(f"New best macro F1: {macro_f1}")

        if micro_f1 > self.best_micro_f1:
            self.best_micro_f1 = micro_f1
            print_info(f"New best micro F1: {micro_f1}")

        # Save model only when combined score improves
        if combined_score > self.best_combined_score:
            self.best_combined_score = combined_score
            self.best_epoch = self.current_epoch
            torch.save(prompt_model.state_dict(), self.ckpt_path)
            improved = True
            print_info(f"New best combined score: {combined_score} (saved checkpoint)")
            print_info(f"Best model is now from epoch {self.current_epoch + 1}")

        if improved:
            self.early_stop_count = 0
        else:
            self.early_stop_count += 1
            print_info(f"No improvement for {self.early_stop_count} epochs. Best model still from epoch {self.best_epoch + 1}")

        self.current_epoch += 1
        return improved

    def generate_embeddings(self):
        """Generate embeddings using the trained model"""
        print_info("Generating embeddings...")

        # Create embedding model with batch_size=8 to match embedding.py
        embedding_args = argparse.Namespace(**vars(self.args))
        embedding_args.batch_size = 8  # Force batch_size to 8 for embedding generation

        prompt_model = EmbeddingModel(
            plm=self.plm,
            template=self.template,
            verbalizer_list=self.verbalizer_list,
            freeze_plm=embedding_args.freeze_plm,
            args=embedding_args,
            processor=self.processor,
            plm_eval_mode=embedding_args.plm_eval_mode,
            use_cuda=self.use_cuda
        )

        # Load trained weights
        if os.path.exists(self.ckpt_path):
            prompt_model.load_state_dict(torch.load(self.ckpt_path, map_location=self.device))
            print_info(f"Loaded weights from {self.ckpt_path}")
        else:
            print_info("Warning: No checkpoint found, using untrained model")

        if self.use_cuda:
            prompt_model = prompt_model.cuda()

        # Create test dataloader with batch_size=8 for embedding generation
        # This matches the logic in embedding.py when test_path exists
        embedding_test_dataloader = SinglePathPromptDataLoader(
            dataset=self.dataset['test'],
            template=self.template,
            tokenizer=self.tokenizer,
            tokenizer_wrapper_class=self.WrapperClass,
            max_seq_length=self.args.max_seq_lens,
            decoder_max_length=3,
            batch_size=8,  # Use batch_size=8 to match embedding.py
            shuffle=False,
            teacher_forcing=False,
            predict_eos_token=False,
            truncate_method="tail",
            num_works=4,
            multi_gpu=True
        )

        # Generate embeddings
        scores = prompt_model.evaluate(
            embedding_test_dataloader,
            self.processor,
            desc="Embedding",
            mode=self.args.eval_mode,
            args=embedding_args
        )

        print_info(f"Embedding generation completed. Saved to {self.embedding_path}")
        return scores

    def evaluate_with_topk(self):
        """Evaluate using top-k similarity"""
        print_info("Evaluating with top-k similarity...")

        # Create topk model with batch_size=8 to match topk.py
        topk_args = argparse.Namespace(**vars(self.args))
        topk_args.batch_size = 8  # Force batch_size to 8 for topk evaluation

        prompt_model = TopkModel(
            plm=self.plm,
            template=self.template,
            verbalizer_list=self.verbalizer_list,
            freeze_plm=topk_args.freeze_plm,
            args=topk_args,
            processor=self.processor,
            plm_eval_mode=topk_args.plm_eval_mode,
            use_cuda=self.use_cuda
        )

        # Load trained weights
        if os.path.exists(self.ckpt_path):
            prompt_model.load_state_dict(torch.load(self.ckpt_path, map_location=self.device))
            print_info(f"Loaded weights from {self.ckpt_path}")
        else:
            print_info("Warning: No checkpoint found, using untrained model")

        if self.use_cuda:
            prompt_model = prompt_model.to(self.device)

        # Load embeddings
        if os.path.exists(self.embedding_path):
            embedding_list = pickle.load(open(self.embedding_path, "rb"))
            print_info(f"Loaded embeddings from {self.embedding_path}")
        else:
            print_info(f"Error: Embedding file {self.embedding_path} not found")
            return None

        # Prepare embedding store
        embedding_store = {'embedding': [], 'label': embedding_list['label']}
        for i in range(len(embedding_list['embedding'])):
            embedding_store['embedding'].append(torch.Tensor(embedding_list['embedding'][i][1]))

        embedding_store['embedding'] = torch.stack(embedding_store['embedding'], dim=0).to(self.device)

        # Create test dataloader with batch_size=8 for topk evaluation
        # This matches the logic in topk.py
        topk_test_dataloader = torch.load(
            os.path.join(f"/data/zhousiqi/TACL_math1/DCL/dataset",
                        "WebOfScience" if self.args.dataset == "wos" else "DBpedia",
                        f"test_dataloader-multi_mask.pt"),
            weights_only=False
        )

        # Evaluate
        scores, similar_samples = prompt_model.evaluate(
            topk_test_dataloader,
            self.processor,
            embedding_store,
            topk=topk_args.topk,
            desc="TopK",
            mode=topk_args.eval_mode
        )

        macro_f1 = scores['macro_f1']
        micro_f1 = scores['micro_f1']
        print_info(f'TopK evaluation - macro {macro_f1} micro {micro_f1}')

        return scores

    def run_integrated_training(self):
        """Run the complete integrated training pipeline"""
        print_info("Starting integrated training pipeline...")
        print_info(f"Run ID: {self.run_id}")

        # Initialize best scores
        best_combined_score = 0.0
        best_epoch = -1

        for epoch in range(self.args.max_epochs):
            # Check early stopping
            if self.early_stop_count >= self.args.early_stop:
                print_info(f"Early stopping at epoch {epoch}")
                break

            # Train one epoch
            improved = self.train_one_epoch()

            if improved:
                # Generate embeddings with current model
                embedding_scores = self.generate_embeddings()

                # Evaluate with top-k
                topk_scores = self.evaluate_with_topk()

                if topk_scores is not None:
                    current_macro = topk_scores['macro_f1']
                    current_micro = topk_scores['micro_f1']
                    combined_score = (current_macro + current_micro) / 2

                    print_info(f"Epoch {epoch + 1}: macro={current_macro:.4f}, micro={current_micro:.4f}, combined={combined_score:.4f}")

                    # Log to TensorBoard
                    self.writer.add_scalar('TopK/macro_f1', current_macro, epoch)
                    self.writer.add_scalar('TopK/micro_f1', current_micro, epoch)
                    self.writer.add_scalar('TopK/combined_score', combined_score, epoch)

                    if combined_score > best_combined_score:
                        best_combined_score = combined_score
                        best_epoch = epoch
                        print_info(f"New best combined score: {combined_score:.4f}")

                        # Save best results
                        best_results = {
                            'epoch': epoch + 1,
                            'macro_f1': current_macro,
                            'micro_f1': current_micro,
                            'combined_score': combined_score,
                            'checkpoint_path': self.ckpt_path,
                            'embedding_path': self.embedding_path
                        }

                        with open(f"result/best_results_{self.run_id}.txt", "w") as f:
                            for key, value in best_results.items():
                                f.write(f"{key}: {value}\n")

        # Final evaluation with best model
        print_info("=" * 50)
        print_info("FINAL RESULTS")
        print_info("=" * 50)
        print_info(f"Training stopped at epoch: {self.current_epoch}")
        print_info(f"Best model from epoch: {self.best_epoch + 1}")
        print_info(f"Best validation combined score: {self.best_combined_score:.4f}")
        print_info(f"Using checkpoint: {self.ckpt_path}")

        # Run final evaluation with the best saved model
        print_info("Running final evaluation with best model...")
        final_embedding_scores = self.generate_embeddings()
        final_topk_scores = self.evaluate_with_topk()

        if final_topk_scores is not None:
            final_combined = (final_topk_scores['macro_f1'] + final_topk_scores['micro_f1']) / 2
            print_info(f"Final test macro F1: {final_topk_scores['macro_f1']:.4f}")
            print_info(f"Final test micro F1: {final_topk_scores['micro_f1']:.4f}")
            print_info(f"Final test combined score: {final_combined:.4f}")

            # Log final results to TensorBoard
            self.writer.add_scalar('Final/macro_f1', final_topk_scores['macro_f1'], self.current_epoch)
            self.writer.add_scalar('Final/micro_f1', final_topk_scores['micro_f1'], self.current_epoch)
            self.writer.add_scalar('Final/combined_score', final_combined, self.current_epoch)

        self.writer.close()
        return final_topk_scores


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser("Integrated Training Pipeline")

    # Model arguments
    parser.add_argument("--model", type=str, default='bert')
    parser.add_argument("--model_name_or_path", default='/data/models/models/dienstag/chinese-roberta-wwm-ext')
    parser.add_argument("--result_file", type=str, default="/data/zhousiqi/TACL_math1/result/integrated_train.txt")

    # Training arguments
    parser.add_argument("--multi_label", default=0, type=int)
    parser.add_argument("--multi_verb", default=1, type=int)
    parser.add_argument("--constraint_loss", default=0, type=int)
    parser.add_argument("--constraint_alpha", default=0.9, type=float)
    parser.add_argument("--lm_training", default=1, type=int)
    parser.add_argument("--lm_alpha", default=0.7, type=float)
    parser.add_argument("--lr", default=3e-5, type=float)
    parser.add_argument("--lr2", default=1e-4, type=float)
    parser.add_argument("--contrastive_loss", default=0, type=int)
    parser.add_argument("--contrastive_alpha", default=0.9, type=float)
    parser.add_argument("--contrastive_level", default=1, type=int)
    parser.add_argument("--batch_size", default=8, type=int)
    parser.add_argument("--depth", default=9, type=int)
    parser.add_argument("--multi_mask", type=int, default=1)
    parser.add_argument("--dropout", default=0.1, type=float)
    parser.add_argument("--shuffle", default=0, type=int)
    parser.add_argument("--contrastive_logits", default=1, type=int)
    parser.add_argument("--cs_mode", default=0, type=int)
    parser.add_argument("--dataset", default="wos", type=str)
    parser.add_argument("--eval_mode", default=0, type=int)
    parser.add_argument("--use_hier_mean", default=1, type=int)
    parser.add_argument("--freeze_plm", default=0, type=int)
    parser.add_argument("--use_scheduler1", default=1, type=int)
    parser.add_argument("--use_scheduler2", default=1, type=int)
    parser.add_argument("--imbalanced_weight", default=True, type=bool)
    parser.add_argument("--imbalanced_weight_reverse", default=True, type=bool)
    parser.add_argument("--device", default=1, type=int)
    parser.add_argument("--use_multi_gpu", default=False, type=bool)
    parser.add_argument("--use_fp16", default=False, type=bool)
    parser.add_argument("--force_use_all_gpus", default=True, type=bool)
    parser.add_argument("--max_grad_norm", default=1.0, type=float)
    parser.add_argument("--max_seq_lens", default=512, type=int)
    parser.add_argument("--use_new_ct", default=1, type=int)
    parser.add_argument("--use_dropout_sim", default=1, type=int)
    parser.add_argument("--use_withoutWrappedLM", default=False, type=bool)
    parser.add_argument('--mean_verbalizer', default=True, type=bool)
    parser.add_argument("--shot", type=int, default=30)
    parser.add_argument("--label_description", type=int, default=0)
    parser.add_argument("--seed", type=int, default=171)
    parser.add_argument("--plm_eval_mode", default=False)
    parser.add_argument("--verbalizer", type=str, default="soft")
    parser.add_argument("--template_id", default=0, type=int)
    parser.add_argument("--not_manual", default=False, type=int)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=8)
    parser.add_argument("--max_epochs", type=int, default=5)
    parser.add_argument("--early_stop", default=5, type=int)
    parser.add_argument("--eval_full", default=0, type=int)

    # TopK evaluation arguments
    parser.add_argument("--topk", type=int, default=3)

    return parser.parse_args()


def main():
    """Main function"""
    start_time = datetime.now()

    # Parse arguments
    args = parse_arguments()

    # Set up environment
    os.environ["CUDA_LAUNCH_BLOCKING"] = "1"

    # Handle multi-GPU configuration
    if os.environ.get('USE_MULTI_GPU', '').lower() == 'true':
        args.use_multi_gpu = True
        print_info("从环境变量启用多GPU模式")

    if os.environ.get('USE_FP16', '').lower() == 'true':
        args.use_fp16 = True
        print_info("从环境变量启用FP16模式")

    # Handle contrastive loss settings
    if args.contrastive_loss == 0:
        args.contrastive_logits = 0
        args.use_dropout_sim = 0

    # Handle shuffle setting
    if args.shuffle == 1:
        args.shuffle = True
    else:
        args.shuffle = False

    # Set seed for reproducibility
    set_seed(args.seed)

    print_info("Arguments:")
    print_info(args)

    # Create result directory
    if not os.path.exists("result"):
        os.mkdir("result")

    # Initialize trainer
    trainer = IntegratedTrainer(args)

    # Run integrated training
    final_scores = trainer.run_integrated_training()

    # Log final results
    end_time = datetime.now()
    content_write = "=" * 50 + "\n"
    content_write += f"INTEGRATED TRAINING RESULTS\n"
    content_write += f"start_time: {start_time}\n"
    content_write += f"end_time: {end_time}\n"
    content_write += f"duration: {end_time - start_time}\n"
    content_write += f"run_id: {trainer.run_id}\n"

    if final_scores is not None:
        content_write += f"final_macro_f1: {final_scores['macro_f1']:.4f}\n"
        content_write += f"final_micro_f1: {final_scores['micro_f1']:.4f}\n"
        content_write += f"final_combined_score: {(final_scores['macro_f1'] + final_scores['micro_f1']) / 2:.4f}\n"

    content_write += "=" * 50 + "\n\n"

    print_info(content_write)

    # Save results to file
    with open(args.result_file, "a") as fout:
        fout.write(content_write)

    print_info("Integrated training completed successfully!")


if __name__ == "__main__":
    main()
